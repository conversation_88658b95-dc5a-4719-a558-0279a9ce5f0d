#!/usr/bin/env python3
"""
灵活的BERT模型Deep Compression压缩管道
支持多种压缩方案的组合：
- 仅剪枝
- 仅量化  
- 仅哈夫曼编码
- 剪枝+量化
- 量化+哈夫曼编码
- 剪枝+哈夫曼编码
- 剪枝+量化+哈夫曼编码

基于论文: Deep Compression (<PERSON> et al.)
适配PyTorch 2.6和Modal平台
"""

import subprocess
import sys
import os
import time
import argparse
import json
import glob
from datetime import datetime
from pathlib import Path

def setup_directories():
    """创建必要的目录结构"""
    dirs = ['/download/model', '/download/other']
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
    print("✅ 目录结构已准备就绪")

def run_command(cmd, description, log_file=None):
    """运行命令并处理错误"""
    print(f"\n🚀 {description}...")
    print(f"📝 命令: {' '.join(cmd)}")
    print("-" * 50)
    
    # 记录到日志文件
    if log_file:
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*60}\n")
            f.write(f"执行步骤: {description}\n")
            f.write(f"命令: {' '.join(cmd)}\n")
            f.write(f"时间: {datetime.now().isoformat()}\n")
            f.write(f"{'='*60}\n")
    
    try:
        start_time = time.time()
        result = subprocess.run(cmd, check=True, capture_output=False)
        end_time = time.time()
        
        duration = end_time - start_time
        success_msg = f"\n✅ {description}完成! 耗时: {duration:.1f}秒"
        print(success_msg)
        
        if log_file:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"结果: 成功\n")
                f.write(f"耗时: {duration:.1f}秒\n\n")
        
        return True
        
    except subprocess.CalledProcessError as e:
        error_msg = f"\n❌ {description}失败: {e}"
        print(error_msg)
        
        if log_file:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"结果: 失败\n")
                f.write(f"错误: {e}\n\n")
        
        return False
    except KeyboardInterrupt:
        interrupt_msg = f"\n⚠️  {description}被用户中断"
        print(interrupt_msg)
        
        if log_file:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write("结果: 用户中断\n\n")
        
        return False

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        size_mb = os.path.getsize(file_path) / 1024 / 1024
        print(f"   ✅ {description}: {size_mb:.2f} MB")
        return True, size_mb
    else:
        print(f"   ❌ {description}: 文件不存在")
        return False, 0

def get_model_path(model_name, step, extension='.pt'):
    """生成带标记的模型文件路径"""
    return f"/download/model/{model_name}_{step}{extension}"

def save_pipeline_config(config, model_name, timestamp):
    """保存管道配置信息"""
    config_file = f"/download/other/{model_name}_pipeline_config_{timestamp}.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    print(f"📋 配置文件已保存: {config_file}")
    return config_file

def create_pipeline_log(model_name, timestamp):
    """创建管道日志文件"""
    log_file = f"/download/other/{model_name}_pipeline_{timestamp}.log"
    with open(log_file, 'w', encoding='utf-8') as f:
        f.write(f"BERT模型压缩管道日志\n")
        f.write(f"模型标记: {model_name}\n")
        f.write(f"开始时间: {timestamp}\n")
        f.write(f"PyTorch版本: 2.6\n")
        f.write(f"平台: Modal\n")
        f.write("="*60 + "\n\n")
    return log_file

def show_compression_summary(model_name, steps_executed, timestamp):
    """显示压缩总结"""
    print("\n" + "="*60)
    print(f"📊 {model_name} 压缩效果总结")
    print("="*60)
    
    # 文件路径映射
    file_mappings = {
        "original": ("原始模型", get_model_path(model_name, "original")),
        "pruned": ("剪枝后模型", get_model_path(model_name, "pruned")),
        "final": ("微调后模型", get_model_path(model_name, "final")),
        "quantized_fp32": ("量化模型(FP32)", get_model_path(model_name, "quantized")),
        "quantized_8bit": ("量化模型(8bit)", get_model_path(model_name, "quantized_8bit")),
    }
    
    sizes = {}
    original_size = None
    
    # 检查各阶段模型文件
    for key, (name, path) in file_mappings.items():
        if os.path.exists(path):
            size_mb = os.path.getsize(path) / 1024 / 1024
            sizes[key] = size_mb
            if key == "original":
                original_size = size_mb
            print(f"   {name}: {size_mb:.2f} MB")
    
    # 检查哈夫曼编码压缩包
    archives = glob.glob(f"/download/model/{model_name}_huffman_*.tar.gz")
    if not archives:
        # 也检查通用命名的压缩包
        archives = glob.glob("/download/model/bert_huffman_encoded_*.tar.gz")
    
    archive_size = None
    if archives:
        latest_archive = max(archives, key=os.path.getctime)
        archive_size = os.path.getsize(latest_archive) / 1024 / 1024
        sizes["huffman"] = archive_size
        print(f"   哈夫曼编码包: {archive_size:.2f} MB")
    
    # 计算各阶段压缩比
    if original_size:
        print(f"\n🎯 各阶段压缩比:")
        
        if "pruning" in steps_executed and "pruned" in sizes:
            ratio = original_size / sizes["pruned"]
            print(f"   剪枝压缩: {ratio:.2f}x")
        
        if "quantization" in steps_executed and "quantized_8bit" in sizes:
            ratio = original_size / sizes["quantized_8bit"]
            print(f"   量化压缩: {ratio:.2f}x")
        
        if "huffman" in steps_executed and archive_size:
            ratio = original_size / archive_size
            print(f"   哈夫曼压缩: {ratio:.2f}x")
            
        # 计算最终压缩比
        final_size = archive_size if archive_size else (
            sizes.get("quantized_8bit") or sizes.get("quantized_fp32") or 
            sizes.get("final") or sizes.get("pruned") or original_size
        )
        
        if final_size and final_size != original_size:
            total_ratio = original_size / final_size
            reduction_percent = ((original_size - final_size) / original_size * 100)
            print(f"   总压缩比: {total_ratio:.2f}x")
            print(f"   大小减少: {reduction_percent:.1f}%")
    
    # 保存压缩总结
    summary_file = f"/download/other/{model_name}_compression_summary_{timestamp}.txt"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"{model_name} 压缩总结报告\n")
        f.write("="*50 + "\n\n")
        f.write(f"执行步骤: {', '.join(steps_executed)}\n")
        f.write(f"生成时间: {datetime.now().isoformat()}\n\n")
        
        f.write("文件大小统计:\n")
        f.write("-"*20 + "\n")
        for key, (name, path) in file_mappings.items():
            if key in sizes:
                f.write(f"{name}: {sizes[key]:.2f} MB\n")
        
        if archive_size:
            f.write(f"哈夫曼编码包: {archive_size:.2f} MB\n")
        
        if original_size:
            f.write(f"\n压缩效果:\n")
            f.write("-"*20 + "\n")
            if final_size and final_size != original_size:
                total_ratio = original_size / final_size
                reduction_percent = ((original_size - final_size) / original_size * 100)
                f.write(f"总压缩比: {total_ratio:.2f}x\n")
                f.write(f"大小减少: {reduction_percent:.1f}%\n")
    
    print(f"📝 压缩总结已保存: {summary_file}")
    return summary_file

def load_original_model(model_name, log_file):
    """加载原始BERT模型并保存"""
    print("\n🔄 加载原始BERT模型...")
    
    # 使用transformers加载模型并保存
    import torch
    from transformers import AutoModelForSequenceClassification
    
    try:
        model = AutoModelForSequenceClassification.from_pretrained("textattack/bert-base-uncased-ag-news")
        original_path = get_model_path(model_name, "original")
        torch.save(model.state_dict(), original_path)
        
        success, size = check_file_exists(original_path, "原始模型")
        if success:
            print(f"✅ 原始模型已保存: {original_path}")
            return original_path
        else:
            print("❌ 原始模型保存失败")
            return None
            
    except Exception as e:
        print(f"❌ 加载原始模型失败: {e}")
        if log_file:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"加载原始模型失败: {e}\n")
        return None

def execute_pruning(model_name, args, log_file):
    """执行剪枝步骤"""
    print(f"\n1️⃣  权重剪枝和微调")
    print(f"   🎯 方法: {args.prune_method}")
    print(f"   📊 敏感度: {args.sensitivity}")
    print(f"   📈 百分位数: {args.percentile}")

    script_dir = os.path.dirname(__file__)

    # 构建剪枝命令
    pruning_cmd = [
        sys.executable,
        os.path.join(script_dir, "bert_pruning.py"),
        "--model_name", model_name,  # 传递model_name参数
        "--epochs", str(args.epochs),
        "--prune_method", args.prune_method,
        "--batch_size", str(args.batch_size),
    ]

    if args.prune_method == "std":
        pruning_cmd.extend(["--sensitivity", str(args.sensitivity)])
    else:
        pruning_cmd.extend(["--percentile", str(args.percentile)])

    if args.max_samples:
        pruning_cmd.extend(["--max_samples", str(args.max_samples)])

    # 修改bert_pruning.py的保存路径
    original_save_path = get_model_path(model_name, "original")
    pruned_save_path = get_model_path(model_name, "pruned")
    final_save_path = get_model_path(model_name, "final")

    if not run_command(pruning_cmd, "权重剪枝和微调", log_file):
        return False

    # 检查和重命名输出文件（如果需要的话）
    # 注意：新的剪枝代码已经直接保存到正确的路径，所以可能不需要重命名
    default_paths = [
        ("/download/model/original_model.pt", original_save_path),
        ("/download/model/pruned_model.pt", pruned_save_path),
        ("/download/model/final_model.pt", final_save_path)
    ]

    for default_path, target_path in default_paths:
        if os.path.exists(default_path) and default_path != target_path:
            os.rename(default_path, target_path)
            print(f"📁 重命名模型文件: {target_path}")

    # 检查最终模型文件是否存在
    print(f"🔍 检查目标文件: {final_save_path}")
    success, _ = check_file_exists(final_save_path, "微调后模型")

    # 如果目标文件不存在，进行详细的调试
    if not success:
        print(f"⚠️  目标文件不存在: {final_save_path}")
        print("🔍 进行详细调试...")

        # 列出所有模型文件
        import glob
        all_files = glob.glob("/download/model/*")
        print(f"📁 /download/model/ 目录下的所有文件:")
        for f in all_files:
            size = os.path.getsize(f) / 1024 / 1024 if os.path.exists(f) else 0
            print(f"   - {f} ({size:.2f} MB)")

        # 特别检查包含model_name的文件
        matching_files = [f for f in all_files if model_name in f]
        print(f"📁 包含 '{model_name}' 的文件:")
        for f in matching_files:
            print(f"   - {f}")

        # 尝试找到最可能的final文件
        final_candidates = [f for f in all_files if 'final' in f.lower()]
        print(f"📁 包含 'final' 的文件:")
        for f in final_candidates:
            print(f"   - {f}")

        # 如果找到了匹配的文件，尝试使用它
        best_match = None
        for f in all_files:
            if model_name in f and 'final' in f.lower() and f.endswith('.pt'):
                best_match = f
                break

        if best_match:
            print(f"✅ 找到最佳匹配文件: {best_match}")
            if best_match != final_save_path:
                print(f"📁 复制到目标路径: {final_save_path}")
                import shutil
                shutil.copy2(best_match, final_save_path)
            success = True
        else:
            print("❌ 未找到合适的final模型文件")

    return success

def execute_quantization(model_name, args, log_file, input_model_path=None):
    """执行量化步骤"""
    print(f"\n2️⃣  权重量化")
    print(f"   🎯 位数: {args.num_bits} bits")

    script_dir = os.path.dirname(__file__)

    # 确定输入模型路径
    if input_model_path is None:
        input_model_path = get_model_path(model_name, "final")
        if not os.path.exists(input_model_path):
            input_model_path = get_model_path(model_name, "original")

    # 输出路径
    quantized_fp32_path = get_model_path(model_name, "quantized")
    quantized_8bit_path = get_model_path(model_name, "quantized_8bit")

    quantization_cmd = [
        sys.executable,
        os.path.join(script_dir, "bert_weight_quantization.py"),
        "--model_path", input_model_path,
        "--num_bits", str(args.num_bits),
        "--output_path", quantized_fp32_path,
        "--evaluate"
    ]

    if not run_command(quantization_cmd, "权重量化", log_file):
        return False

    # 检查和重命名输出文件
    default_8bit_path = "/download/model/quantized_model_8bit.pt"
    if os.path.exists(default_8bit_path) and default_8bit_path != quantized_8bit_path:
        os.rename(default_8bit_path, quantized_8bit_path)
        print(f"📁 重命名8位模型文件: {quantized_8bit_path}")

    success, _ = check_file_exists(quantized_8bit_path, "8位量化模型")

    return success

def execute_huffman_encoding(model_name, args, log_file, input_model_path=None):
    """执行哈夫曼编码步骤"""
    print(f"\n3️⃣  哈夫曼编码")
    print(f"   🎯 目标: 熵编码进一步压缩")

    script_dir = os.path.dirname(__file__)

    # 确定输入模型路径
    if input_model_path is None:
        input_model_path = get_model_path(model_name, "quantized_8bit")
        if not os.path.exists(input_model_path):
            # 尝试通用路径
            input_model_path = "/download/model/quantized_model_8bit.pt"

    huffman_cmd = [
        sys.executable,
        os.path.join(script_dir, "bert_huffman_encode.py"),
        "--input_path", input_model_path,
        "--output_dir", "/download/",
        "--evaluate",
        "--test_decode"
    ]

    if not run_command(huffman_cmd, "哈夫曼编码", log_file):
        return False

    # 检查是否生成了压缩包
    archives = glob.glob("/download/model/bert_huffman_encoded_*.tar.gz")
    if archives:
        latest_archive = max(archives, key=os.path.getctime)
        # 重命名为带模型名称的压缩包
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        new_archive = f"/download/model/{model_name}_huffman_{timestamp}.tar.gz"
        os.rename(latest_archive, new_archive)
        print(f"📁 重命名压缩包: {new_archive}")

        return True
    else:
        print("❌ 未找到哈夫曼编码压缩包")
        return False

def main():
    parser = argparse.ArgumentParser(description='灵活的BERT模型Deep Compression压缩管道')
    
    # 基本参数
    parser.add_argument('--model_name', type=str, required=True,
                       help='模型标记名称，用于区分不同的压缩实验')
    parser.add_argument('--steps', type=str, required=True,
                       choices=['pruning', 'quantization', 'huffman', 'pruning+quantization', 
                               'quantization+huffman', 'pruning+huffman', 'all'],
                       help='要执行的压缩步骤')
    
    # 剪枝参数
    parser.add_argument('--prune_method', type=str, choices=['percentile', 'std'], default='std',
                       help='剪枝方法 (默认: std)')
    parser.add_argument('--sensitivity', type=float, default=0.25,
                       help='标准差剪枝敏感度 (默认: 0.25)')
    parser.add_argument('--percentile', type=float, default=5.0,
                       help='百分位数剪枝百分位数 (默认: 5.0)')
    parser.add_argument('--epochs', type=int, default=3,
                       help='微调轮数 (默认: 3)')
    parser.add_argument('--batch_size', type=int, default=16,
                       help='批次大小 (默认: 16)')
    parser.add_argument('--max_samples', type=int, default=None,
                       help='最大样本数，用于快速测试')
    
    # 量化参数
    parser.add_argument('--num_bits', type=int, default=8,
                       help='量化位数 (默认: 8)')
    
    # 其他参数
    parser.add_argument('--no_cuda', action='store_true', default=False,
                       help='禁用CUDA')
    
    args = parser.parse_args()
    
    # 解析执行步骤
    steps_to_execute = []
    if args.steps == 'all':
        steps_to_execute = ['pruning', 'quantization', 'huffman']
    elif '+' in args.steps:
        steps_to_execute = args.steps.split('+')
    else:
        steps_to_execute = [args.steps]
    
    print("🏆 BERT模型Deep Compression灵活压缩管道")
    print("="*60)
    print("📚 基于论文: Deep Compression (Han et al.)")
    print(f"🏷️  模型标记: {args.model_name}")
    print(f"🔧 执行步骤: {' → '.join(steps_to_execute)}")
    print(f"⚙️  PyTorch: 2.6 | 平台: Modal")
    print("="*60)
    
    # 设置环境
    setup_directories()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = create_pipeline_log(args.model_name, timestamp)
    
    # 保存配置
    config = {
        "model_name": args.model_name,
        "steps": steps_to_execute,
        "parameters": vars(args),
        "timestamp": timestamp,
        "pytorch_version": "2.6",
        "platform": "Modal"
    }
    config_file = save_pipeline_config(config, args.model_name, timestamp)
    
    # 加载原始模型
    original_model_path = load_original_model(args.model_name, log_file)
    if not original_model_path:
        print("❌ 无法加载原始模型，终止流程")
        return False

    # 执行压缩步骤
    current_model_path = original_model_path
    success = True
    
    for i, step in enumerate(steps_to_execute, 1):
        print(f"\n{'='*60}")
        print(f"执行第 {i}/{len(steps_to_execute)} 步: {step.upper()}")
        print("="*60)

        if step == 'pruning':
            success = execute_pruning(args.model_name, args, log_file)
            if success:
                current_model_path = get_model_path(args.model_name, "final")

        elif step == 'quantization':
            success = execute_quantization(args.model_name, args, log_file, current_model_path)
            if success:
                current_model_path = get_model_path(args.model_name, "quantized_8bit")

        elif step == 'huffman':
            success = execute_huffman_encoding(args.model_name, args, log_file, current_model_path)

        if not success:
            print(f"❌ {step}步骤失败，终止流程")
            break
    
    # 显示最终总结
    if success:
        summary_file = show_compression_summary(args.model_name, steps_to_execute, timestamp)

        print("\n" + "="*60)
        print("🎉 压缩管道执行成功!")
        print("="*60)

        print(f"\n📁 生成的关键文件:")
        print(f"   📋 配置文件: {config_file}")
        print(f"   📄 执行日志: {log_file}")
        print(f"   📊 压缩总结: {summary_file}")

        print(f"\n📂 模型文件 (/download/model/):")
        for step in steps_to_execute:
            if step == 'pruning':
                print(f"   🔸 剪枝后模型: {get_model_path(args.model_name, 'final')}")
            elif step == 'quantization':
                print(f"   🔸 量化模型: {get_model_path(args.model_name, 'quantized_8bit')}")
            elif step == 'huffman':
                archives = glob.glob(f"/download/model/{args.model_name}_huffman_*.tar.gz")
                if archives:
                    latest = max(archives, key=os.path.getctime)
                    print(f"   🔸 哈夫曼压缩包: {latest}")

        print(f"\n💡 技术摘要:")
        if 'pruning' in steps_to_execute:
            print(f"   🔸 剪枝: {args.prune_method}方法，敏感度{args.sensitivity}")
        if 'quantization' in steps_to_execute:
            print(f"   🔸 量化: K-means聚类，{args.num_bits}位权重共享")
        if 'huffman' in steps_to_execute:
            print(f"   🔸 哈夫曼: 自适应熵编码无损压缩")


    
    return success

def run_compression(model_name, steps, **kwargs):
    """
    便于Modal平台调用的包装函数
    
    Args:
        model_name (str): 模型标记名称
        steps (str): 执行步骤 ('pruning', 'quantization', 'huffman', 'pruning+quantization', 
                    'quantization+huffman', 'pruning+huffman', 'all')
        **kwargs: 其他参数
            - prune_method (str): 剪枝方法 ('std' 或 'percentile')
            - sensitivity (float): 标准差剪枝敏感度
            - percentile (float): 百分位数剪枝百分位数
            - epochs (int): 微调轮数
            - batch_size (int): 批次大小
            - max_samples (int): 最大样本数
            - num_bits (int): 量化位数
            - no_cuda (bool): 禁用CUDA
    
    Returns:
        bool: 执行是否成功
    
    Example:
        # 完整压缩流程
        success = run_compression('experiment_v1', 'all')
        
        # 仅剪枝
        success = run_compression('prune_test', 'pruning', 
                                 prune_method='std', sensitivity=0.25, epochs=3)
        
        # 剪枝+量化
        success = run_compression('prune_quant', 'pruning+quantization',
                                 sensitivity=0.3, num_bits=6, max_samples=5000)
    """
    import sys
    
    # 构建参数列表
    args_list = ['--model_name', model_name, '--steps', steps]
    
    # 添加可选参数，设置合理的默认值
    # 剪枝相关参数
    prune_method = kwargs.get('prune_method', 'std')
    args_list.extend(['--prune_method', str(prune_method)])

    if 'sensitivity' in kwargs:
        args_list.extend(['--sensitivity', str(kwargs['sensitivity'])])
    if 'percentile' in kwargs:
        args_list.extend(['--percentile', str(kwargs['percentile'])])

    # 训练相关参数
    if 'epochs' in kwargs:
        args_list.extend(['--epochs', str(kwargs['epochs'])])
    if 'batch_size' in kwargs:
        args_list.extend(['--batch_size', str(kwargs['batch_size'])])
    if 'max_samples' in kwargs:
        args_list.extend(['--max_samples', str(kwargs['max_samples'])])

    # 量化相关参数
    if 'num_bits' in kwargs:
        args_list.extend(['--num_bits', str(kwargs['num_bits'])])

    # 其他参数
    if kwargs.get('no_cuda', False):
        args_list.append('--no_cuda')
    
    # 备份原始sys.argv
    original_argv = sys.argv[:]
    
    try:
        # 临时替换sys.argv
        sys.argv = ['run_full_compression.py'] + args_list
        
        # 调用main函数
        return main()
        
    finally:
        # 恢复原始sys.argv
        sys.argv = original_argv

if __name__ == '__main__':
    start_time = datetime.now()
    
    try:
        success = main()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n⏱️  总耗时: {duration}")
        print("="*60)
        
        if success:
            print("✅ 所有步骤成功完成!")
            print("🎊 BERT模型Deep Compression压缩管道执行完毕!")
            print("\n🚀 Modal平台兼容性: 已完全适配PyTorch 2.6")
            print("📂 所有文件已保存到 /download/ 目录")
        else:
            print("❌ 部分步骤失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  流程被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)