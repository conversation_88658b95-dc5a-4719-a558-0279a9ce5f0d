# Deep Compression PyTorch - code_version1 目录文件说明

## 📋 文件分类总览

### 🎯 核心算法 (3个文件)
- `bert_pruning.py` - 剪枝算法核心实现
- `bert_weight_quantization.py` - 权重量化实现  
- `bert_huffman_encode.py` - 哈夫曼编码实现

### 🚀 运行脚本 (5个文件)
- `run_full_compression.py` - 主控制器，支持灵活压缩组合
- `run_modal.py` - Modal云平台适配
- `run_bert_pruning_modal.py` - 剪枝Modal专用运行器
- `run_quantization.py` - 量化快速运行脚本
- `run_huffman_encode.py` - 哈夫曼编码快速运行脚本

### 🔧 工具文件 (1个文件)
- `debug_file_paths.py` - 文件路径调试工具

## 🔄 执行流程关系

### 完整压缩管道
```
run_modal.py (云端入口)
    ↓
run_full_compression.py (主控制器)
    ↓
bert_pruning.py → bert_weight_quantization.py → bert_huffman_encode.py
```

### 单独执行
```
run_bert_pruning_modal.py → bert_pruning.py
run_quantization.py → bert_weight_quantization.py  
run_huffman_encode.py → bert_huffman_encode.py
```
