#!/usr/bin/env python3
"""
BERT 模型哈夫曼编码脚本
对剪枝+量化后的BERT模型进行哈夫曼编码，进一步提高压缩率

主要功能：
1. 加载8位量化后的BERT模型
2. 对量化索引(uint8)和码表(float32)分别进行哈夫曼编码
3. 对非量化参数进行哈夫曼编码
4. 保存编码后的模型和解码信息
5. 计算压缩比和性能评估

使用方法：
python bert_huffman_encode.py --input_path /download/model/quantized_model_8bit.pt
"""

import argparse
import os
import torch
import numpy as np
from collections import defaultdict, namedtuple
from heapq import heappush, heappop, heapify
import struct
from pathlib import Path
import logging
from datetime import datetime
import json
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from datasets import load_dataset
from tqdm import tqdm

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 哈夫曼编码相关数据结构
Node = namedtuple('Node', 'freq value left right')
Node.__lt__ = lambda x, y: x.freq < y.freq

def safe_torch_load(file_path, map_location='cpu'):
    """
    安全加载torch文件，兼容PyTorch 2.6的weights_only模式
    """
    try:
        # 首先尝试weights_only=True（安全模式）
        return torch.load(file_path, map_location=map_location, weights_only=True)
    except Exception as e1:
        logger.warning(f"weights_only=True加载失败，尝试添加安全全局变量")
        try:
            # 尝试添加numpy相关的安全全局变量
            safe_globals_list = [np.ndarray]
            
            # 尝试添加不同版本的numpy重建函数
            try:
                from numpy.core.multiarray import _reconstruct
                safe_globals_list.append(_reconstruct)
            except ImportError:
                try:
                    from numpy._core.multiarray import _reconstruct
                    safe_globals_list.append(_reconstruct)
                except ImportError:
                    logger.warning("无法导入numpy._reconstruct，跳过")
            
            # 添加其他可能需要的numpy函数
            try:
                import numpy.core.numeric as numeric
                safe_globals_list.extend([numeric.dtype, numeric.array])
            except ImportError:
                pass
                
            with torch.serialization.safe_globals(safe_globals_list):
                return torch.load(file_path, map_location=map_location, weights_only=True)
        except Exception as e2:
            logger.warning(f"safe_globals加载失败，使用兼容模式")
            try:
                # 最后尝试weights_only=False（兼容模式）
                logger.warning("使用weights_only=False模式加载文件（信任源）")
                return torch.load(file_path, map_location=map_location, weights_only=False)
            except Exception as e3:
                logger.error(f"所有加载方式都失败: {e3}")
                raise e3

def setup_save_directories():
    """创建保存文件所需的目录结构"""
    dirs = ['/download/model', '/download/other']
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
        logger.info(f"✓ Directory ready: {dir_path}")

def huffman_encode_array(arr, dtype='auto'):
    """
    对numpy数组进行哈夫曼编码
    
    Args:
        arr: 要编码的numpy数组
        dtype: 数据类型 ('uint8', 'float32', 'auto')
    
    Returns:
        tuple: (编码后的二进制字符串, 哈夫曼树, 原始形状, 数据类型)
    """
    # 自动推断数据类型
    if dtype == 'auto':
        if arr.dtype == np.uint8:
            dtype = 'uint8'
        elif arr.dtype == np.float32:
            dtype = 'float32'
        else:
            dtype = 'float32'  # 默认
    
    original_shape = arr.shape
    flat_arr = arr.flatten()
    
    # 计算频率
    freq_map = defaultdict(int)
    convert_map = {'float32': float, 'uint8': int}
    
    for value in flat_arr:
        value = convert_map[dtype](value)
        freq_map[value] += 1
    
    # 如果只有一个唯一值，特殊处理
    if len(freq_map) == 1:
        unique_value = list(freq_map.keys())[0]
        return '', unique_value, original_shape, dtype, True
    
    # 构建哈夫曼树
    heap = [Node(frequency, value, None, None) for value, frequency in freq_map.items()]
    heapify(heap)
    
    while len(heap) > 1:
        node1 = heappop(heap)
        node2 = heappop(heap)
        merged = Node(node1.freq + node2.freq, None, node1, node2)
        heappush(heap, merged)
    
    # 生成编码表
    value2code = {}
    
    def generate_code(node, code):
        if node is None:
            return
        if node.value is not None:
            value2code[node.value] = code
            return
        generate_code(node.left, code + '0')
        generate_code(node.right, code + '1')
    
    root = heappop(heap)
    generate_code(root, '')
    
    # 编码数据
    data_encoding = ''.join(value2code[convert_map[dtype](value)] for value in flat_arr)
    
    return data_encoding, root, original_shape, dtype, False

def huffman_decode_array(data_encoding, huffman_tree, original_shape, dtype, is_single_value=False):
    """
    从哈夫曼编码解码数组
    """
    if is_single_value:
        # 只有一个唯一值的情况
        return np.full(original_shape, huffman_tree, dtype=getattr(np, dtype))
    
    if not data_encoding:
        return np.array([])
    
    # 解码数据
    data = []
    ptr = huffman_tree
    
    for bit in data_encoding:
        ptr = ptr.left if bit == '0' else ptr.right
        if ptr.value is not None:  # 叶子节点
            data.append(ptr.value)
            ptr = huffman_tree
    
    return np.array(data, dtype=getattr(np, dtype)).reshape(original_shape)

def encode_huffman_tree(root, dtype):
    """
    编码哈夫曼树为二进制字符串
    """
    converter = {'float32': float2bitstr, 'uint8': uint8_2bitstr}
    code_list = []
    
    def encode_node(node):
        if node.value is not None:  # 叶子节点
            code_list.append('1')
            lst = list(converter[dtype](node.value))
            code_list.extend(lst)
        else:
            code_list.append('0')
            encode_node(node.left)
            encode_node(node.right)
    
    encode_node(root)
    return ''.join(code_list)

def decode_huffman_tree(code_str, dtype):
    """
    从二进制字符串解码哈夫曼树
    """
    converter = {'float32': bitstr2float, 'uint8': bitstr2uint8}
    bits_per_value = {'float32': 32, 'uint8': 8}
    idx = 0
    
    def decode_node():
        nonlocal idx
        info = code_str[idx]
        idx += 1
        if info == '1':  # 叶子节点
            value = converter[dtype](code_str[idx:idx+bits_per_value[dtype]])
            idx += bits_per_value[dtype]
            return Node(0, value, None, None)
        else:
            left = decode_node()
            right = decode_node()
            return Node(0, None, left, right)
    
    return decode_node()

def dump_binary_data(code_str, filename):
    """
    将二进制字符串保存到文件
    """
    if not code_str:
        # 空字符串的情况
        with open(filename, 'wb') as f:
            f.write(b'\x00')  # 写入一个0字节表示空数据
        return 1
    
    # 添加padding
    num_of_padding = (-len(code_str)) % 8
    header = f"{num_of_padding:08b}"
    code_str = header + code_str + '0' * num_of_padding
    
    # 转换为字节
    byte_arr = bytearray(int(code_str[i:i+8], 2) for i in range(0, len(code_str), 8))
    
    with open(filename, 'wb') as f:
        f.write(byte_arr)
    
    return len(byte_arr)

def load_binary_data(filename):
    """
    从文件加载二进制字符串
    """
    with open(filename, 'rb') as f:
        data = f.read()
    
    if len(data) == 1 and data[0] == 0:
        # 空数据的情况
        return ''
    
    if len(data) == 0:
        return ''
    
    header = data[0]
    rest = data[1:]
    
    if len(rest) == 0:
        return ''
    
    code_str = ''.join(f'{byte:08b}' for byte in rest)
    offset = header
    
    if offset != 0:
        code_str = code_str[:-offset]
    
    return code_str

# 数据类型转换函数
def float2bitstr(f):
    """将float32转换为32位二进制字符串"""
    four_bytes = struct.pack('>f', f)
    return ''.join(f'{byte:08b}' for byte in four_bytes)

def bitstr2float(bitstr):
    """将32位二进制字符串转换为float32"""
    byte_arr = bytearray(int(bitstr[i:i+8], 2) for i in range(0, len(bitstr), 8))
    return struct.unpack('>f', byte_arr)[0]

def uint8_2bitstr(value):
    """将uint8转换为8位二进制字符串"""
    return f'{int(value):08b}'

def bitstr2uint8(bitstr):
    """将8位二进制字符串转换为uint8"""
    return int(bitstr, 2)

def huffman_encode_quantized_model(quantized_model_path, output_dir='/download/'):
    """
    对8位量化模型进行哈夫曼编码
    
    Args:
        quantized_model_path: 8位量化模型路径
        output_dir: 输出目录
    
    Returns:
        编码统计信息
    """
    logger.info(f"🔄 加载8位量化模型: {quantized_model_path}")
    
    # 加载量化模型数据
    quantized_data = safe_torch_load(quantized_model_path, map_location='cpu')
    quantization_info = quantized_data['quantization_info']
    non_quantized_params = quantized_data.get('non_quantized_params', {})
    
    logger.info(f"📊 量化模型信息:")
    logger.info(f"   - 量化层数: {len(quantization_info)}")
    logger.info(f"   - 非量化参数数: {len(non_quantized_params)}")
    
    # 创建编码输出目录
    encoding_dir = os.path.join(output_dir, 'model', 'huffman_encoded')
    os.makedirs(encoding_dir, exist_ok=True)
    
    # 统计信息
    encoding_stats = {
        'quantized_layers': {},
        'non_quantized_params': {},
        'total_original_size': 0,
        'total_encoded_size': 0,
        'compression_ratio': 0.0
    }
    
    logger.info("🔄 开始哈夫曼编码量化层...")
    
    # 对量化层进行哈夫曼编码
    for layer_name, layer_info in tqdm(quantization_info.items(), desc="编码量化层"):
        indices = layer_info['indices']
        codebook = layer_info['codebook']
        original_shape = layer_info['original_shape']
        non_zero_mask = layer_info['non_zero_mask']
        
        layer_stats = {
            'original_size': 0,
            'encoded_size': 0,
            'files': {}
        }
        
        # 编码索引 (uint8)
        indices_encoding, indices_tree, indices_shape, indices_dtype, indices_single = huffman_encode_array(indices, 'uint8')
        
        # 保存索引编码
        indices_data_file = os.path.join(encoding_dir, f'{layer_name}_indices_data.bin')
        indices_tree_file = os.path.join(encoding_dir, f'{layer_name}_indices_tree.bin')
        indices_meta_file = os.path.join(encoding_dir, f'{layer_name}_indices_meta.json')
        
        indices_data_size = dump_binary_data(indices_encoding, indices_data_file)
        
        if not indices_single:
            indices_tree_encoding = encode_huffman_tree(indices_tree, indices_dtype)
            indices_tree_size = dump_binary_data(indices_tree_encoding, indices_tree_file)
        else:
            indices_tree_size = 0
        
        # 保存索引元数据
        indices_meta = {
            'shape': indices_shape,
            'dtype': indices_dtype,
            'is_single_value': indices_single,
            'single_value': indices_tree if indices_single else None
        }
        with open(indices_meta_file, 'w') as f:
            json.dump(indices_meta, f)
        
        layer_stats['files']['indices_data'] = indices_data_file
        layer_stats['files']['indices_tree'] = indices_tree_file
        layer_stats['files']['indices_meta'] = indices_meta_file
        
        # 编码码表 (float32)
        codebook_encoding, codebook_tree, codebook_shape, codebook_dtype, codebook_single = huffman_encode_array(codebook, 'float32')
        
        # 保存码表编码
        codebook_data_file = os.path.join(encoding_dir, f'{layer_name}_codebook_data.bin')
        codebook_tree_file = os.path.join(encoding_dir, f'{layer_name}_codebook_tree.bin')
        codebook_meta_file = os.path.join(encoding_dir, f'{layer_name}_codebook_meta.json')
        
        codebook_data_size = dump_binary_data(codebook_encoding, codebook_data_file)
        
        if not codebook_single:
            codebook_tree_encoding = encode_huffman_tree(codebook_tree, codebook_dtype)
            codebook_tree_size = dump_binary_data(codebook_tree_encoding, codebook_tree_file)
        else:
            codebook_tree_size = 0
        
        # 保存码表元数据
        codebook_meta = {
            'shape': codebook_shape,
            'dtype': codebook_dtype,
            'is_single_value': codebook_single,
            'single_value': float(codebook_tree) if codebook_single else None
        }
        with open(codebook_meta_file, 'w') as f:
            json.dump(codebook_meta, f)
        
        layer_stats['files']['codebook_data'] = codebook_data_file
        layer_stats['files']['codebook_tree'] = codebook_tree_file
        layer_stats['files']['codebook_meta'] = codebook_meta_file
        
        # 保存非零掩码 (bool -> uint8)
        mask_array = non_zero_mask.astype(np.uint8)
        mask_encoding, mask_tree, mask_shape, mask_dtype, mask_single = huffman_encode_array(mask_array, 'uint8')
        
        mask_data_file = os.path.join(encoding_dir, f'{layer_name}_mask_data.bin')
        mask_tree_file = os.path.join(encoding_dir, f'{layer_name}_mask_tree.bin')
        mask_meta_file = os.path.join(encoding_dir, f'{layer_name}_mask_meta.json')
        
        mask_data_size = dump_binary_data(mask_encoding, mask_data_file)
        
        if not mask_single:
            mask_tree_encoding = encode_huffman_tree(mask_tree, mask_dtype)
            mask_tree_size = dump_binary_data(mask_tree_encoding, mask_tree_file)
        else:
            mask_tree_size = 0
        
        # 保存掩码元数据
        mask_meta = {
            'shape': mask_shape,
            'dtype': mask_dtype,
            'is_single_value': mask_single,
            'single_value': int(mask_tree) if mask_single else None,
            'original_shape': original_shape  # 保存原始张量形状
        }
        with open(mask_meta_file, 'w') as f:
            json.dump(mask_meta, f)
        
        layer_stats['files']['mask_data'] = mask_data_file
        layer_stats['files']['mask_tree'] = mask_tree_file
        layer_stats['files']['mask_meta'] = mask_meta_file
        
        # 计算层级压缩统计
        original_size = indices.nbytes + codebook.nbytes + non_zero_mask.nbytes
        encoded_size = (indices_data_size + indices_tree_size + 
                       codebook_data_size + codebook_tree_size + 
                       mask_data_size + mask_tree_size)
        
        layer_stats['original_size'] = original_size
        layer_stats['encoded_size'] = encoded_size
        layer_stats['compression_ratio'] = original_size / encoded_size if encoded_size > 0 else 0
        
        encoding_stats['quantized_layers'][layer_name] = layer_stats
        encoding_stats['total_original_size'] += original_size
        encoding_stats['total_encoded_size'] += encoded_size
        
        logger.debug(f"   {layer_name}: {original_size} -> {encoded_size} bytes "
                    f"({layer_stats['compression_ratio']:.2f}x)")
    
    # 对非量化参数进行哈夫曼编码
    logger.info("🔄 开始哈夫曼编码非量化参数...")
    
    for param_name, param_tensor in tqdm(non_quantized_params.items(), desc="编码非量化参数"):
        param_array = param_tensor.cpu().numpy()
        
        # 对参数进行哈夫曼编码
        param_encoding, param_tree, param_shape, param_dtype, param_single = huffman_encode_array(param_array, 'float32')
        
        # 保存参数编码
        param_data_file = os.path.join(encoding_dir, f'{param_name.replace(".", "_")}_data.bin')
        param_tree_file = os.path.join(encoding_dir, f'{param_name.replace(".", "_")}_tree.bin')
        param_meta_file = os.path.join(encoding_dir, f'{param_name.replace(".", "_")}_meta.json')
        
        param_data_size = dump_binary_data(param_encoding, param_data_file)
        
        if not param_single:
            param_tree_encoding = encode_huffman_tree(param_tree, param_dtype)
            param_tree_size = dump_binary_data(param_tree_encoding, param_tree_file)
        else:
            param_tree_size = 0
        
        # 保存参数元数据
        param_meta = {
            'param_name': param_name,
            'shape': param_shape,
            'dtype': param_dtype,
            'is_single_value': param_single,
            'single_value': float(param_tree) if param_single else None
        }
        with open(param_meta_file, 'w') as f:
            json.dump(param_meta, f)
        
        # 统计
        original_size = param_array.nbytes
        encoded_size = param_data_size + param_tree_size
        
        param_stats = {
            'original_size': original_size,
            'encoded_size': encoded_size,
            'compression_ratio': original_size / encoded_size if encoded_size > 0 else 0,
            'files': {
                'data': param_data_file,
                'tree': param_tree_file,
                'meta': param_meta_file
            }
        }
        
        encoding_stats['non_quantized_params'][param_name] = param_stats
        encoding_stats['total_original_size'] += original_size
        encoding_stats['total_encoded_size'] += encoded_size
        
        logger.debug(f"   {param_name}: {original_size} -> {encoded_size} bytes "
                    f"({param_stats['compression_ratio']:.2f}x)")
    
    # 保存模型配置和其他元数据
    model_meta = {
        'model_config': quantized_data.get('model_config'),
        'num_bits': quantized_data.get('num_bits', 8),
        'format': 'huffman_encoded_quantized',
        'original_format': quantized_data.get('format', 'uint8_indices_with_codebook')
    }
    
    model_meta_file = os.path.join(encoding_dir, 'model_meta.json')
    with open(model_meta_file, 'w') as f:
        json.dump(model_meta, f, indent=2)
    
    # 计算总体压缩比
    encoding_stats['compression_ratio'] = (encoding_stats['total_original_size'] / 
                                         encoding_stats['total_encoded_size'] 
                                         if encoding_stats['total_encoded_size'] > 0 else 0)
    
    logger.info(f"🎉 哈夫曼编码完成!")
    logger.info(f"📊 总体压缩: {encoding_stats['total_original_size']} -> {encoding_stats['total_encoded_size']} bytes")
    logger.info(f"🗜️  压缩比: {encoding_stats['compression_ratio']:.2f}x")
    
    return encoding_stats, encoding_dir

def decode_huffman_encoded_model(encoding_dir, device='cpu'):
    """
    解码哈夫曼编码的模型
    
    Args:
        encoding_dir: 编码文件目录
        device: 目标设备
    
    Returns:
        解码后的模型
    """
    logger.info(f"🔄 解码哈夫曼编码模型: {encoding_dir}")
    
    # 加载模型元数据
    model_meta_file = os.path.join(encoding_dir, 'model_meta.json')
    with open(model_meta_file, 'r') as f:
        model_meta = json.load(f)
    
    # 创建基础模型
    model_name = "textattack/bert-base-uncased-ag-news"
    model = AutoModelForSequenceClassification.from_pretrained(model_name)
    model.to(device)
    
    # 查找所有编码文件
    encoding_files = os.listdir(encoding_dir)
    
    # 解码量化层
    quantized_layers = {}
    for file_name in encoding_files:
        if file_name.endswith('_indices_meta.json'):
            layer_name = file_name.replace('_indices_meta.json', '')
            
            # 加载索引
            indices_meta_file = os.path.join(encoding_dir, f'{layer_name}_indices_meta.json')
            with open(indices_meta_file, 'r') as f:
                indices_meta = json.load(f)
            
            if indices_meta['is_single_value']:
                indices = np.full(indices_meta['shape'], indices_meta['single_value'], dtype=np.uint8)
            else:
                indices_data = load_binary_data(os.path.join(encoding_dir, f'{layer_name}_indices_data.bin'))
                indices_tree_data = load_binary_data(os.path.join(encoding_dir, f'{layer_name}_indices_tree.bin'))
                indices_tree = decode_huffman_tree(indices_tree_data, indices_meta['dtype'])
                indices = huffman_decode_array(indices_data, indices_tree, indices_meta['shape'], indices_meta['dtype'])
            
            # 加载码表
            codebook_meta_file = os.path.join(encoding_dir, f'{layer_name}_codebook_meta.json')
            with open(codebook_meta_file, 'r') as f:
                codebook_meta = json.load(f)
            
            if codebook_meta['is_single_value']:
                codebook = np.array([codebook_meta['single_value']], dtype=np.float32)
            else:
                codebook_data = load_binary_data(os.path.join(encoding_dir, f'{layer_name}_codebook_data.bin'))
                codebook_tree_data = load_binary_data(os.path.join(encoding_dir, f'{layer_name}_codebook_tree.bin'))
                codebook_tree = decode_huffman_tree(codebook_tree_data, codebook_meta['dtype'])
                codebook = huffman_decode_array(codebook_data, codebook_tree, codebook_meta['shape'], codebook_meta['dtype'])
            
            # 加载掩码
            mask_meta_file = os.path.join(encoding_dir, f'{layer_name}_mask_meta.json')
            with open(mask_meta_file, 'r') as f:
                mask_meta = json.load(f)
            
            if mask_meta['is_single_value']:
                non_zero_mask = np.full(mask_meta['shape'], mask_meta['single_value'], dtype=bool)
            else:
                mask_data = load_binary_data(os.path.join(encoding_dir, f'{layer_name}_mask_data.bin'))
                mask_tree_data = load_binary_data(os.path.join(encoding_dir, f'{layer_name}_mask_tree.bin'))
                mask_tree = decode_huffman_tree(mask_tree_data, mask_meta['dtype'])
                mask_array = huffman_decode_array(mask_data, mask_tree, mask_meta['shape'], mask_meta['dtype'])
                non_zero_mask = mask_array.astype(bool)
            
            quantized_layers[layer_name] = {
                'indices': indices,
                'codebook': codebook,
                'original_shape': mask_meta['original_shape'],
                'non_zero_mask': non_zero_mask
            }
    
    # 重建量化权重
    for name, module in model.named_modules():
        if name in quantized_layers:
            layer_info = quantized_layers[name]
            
            # 重建权重
            flat_tensor = np.zeros(layer_info['indices'].shape, dtype=np.float32)
            flat_tensor[layer_info['non_zero_mask']] = layer_info['codebook'][layer_info['indices'][layer_info['non_zero_mask']]]
            reconstructed_weight = torch.from_numpy(flat_tensor.reshape(layer_info['original_shape']))
            
            module.weight.data = reconstructed_weight.to(device)
    
    # 解码非量化参数
    for file_name in encoding_files:
        if (file_name.endswith('_meta.json') and 
            not file_name.endswith('_indices_meta.json') and 
            not file_name.endswith('_codebook_meta.json') and 
            not file_name.endswith('_mask_meta.json') and
            file_name != 'model_meta.json'):
            
            param_meta_file = os.path.join(encoding_dir, file_name)
            with open(param_meta_file, 'r') as f:
                param_meta = json.load(f)
            
            param_name = param_meta['param_name']
            base_name = file_name.replace('_meta.json', '')
            
            if param_meta['is_single_value']:
                param_array = np.full(param_meta['shape'], param_meta['single_value'], dtype=np.float32)
            else:
                param_data = load_binary_data(os.path.join(encoding_dir, f'{base_name}_data.bin'))
                param_tree_data = load_binary_data(os.path.join(encoding_dir, f'{base_name}_tree.bin'))
                param_tree = decode_huffman_tree(param_tree_data, param_meta['dtype'])
                param_array = huffman_decode_array(param_data, param_tree, param_meta['shape'], param_meta['dtype'])
            
            # 设置参数
            param_tensor = torch.from_numpy(param_array).to(device)
            model.state_dict()[param_name].copy_(param_tensor)
    
    logger.info("✅ 哈夫曼编码模型解码完成!")
    return model

def evaluate_model(model, tokenizer, device, sample_size=1000):
    """
    快速评估模型性能
    """
    logger.info("🔄 评估模型性能...")
    dataset = load_dataset("ag_news", split="test")
    
    # 随机采样
    if len(dataset) > sample_size:
        indices = np.random.choice(len(dataset), sample_size, replace=False)
        dataset = dataset.select(indices)
    
    model.eval()
    correct = 0
    total = 0
    
    with torch.no_grad():
        for example in tqdm(dataset, desc="评估中"):
            text = example['text']
            label = example['label']
            
            inputs = tokenizer(
                text, 
                return_tensors="pt", 
                truncation=True, 
                padding=True, 
                max_length=512
            ).to(device)
            
            outputs = model(**inputs)
            predicted = outputs.logits.argmax(dim=-1).item()
            
            if predicted == label:
                correct += 1
            total += 1
    
    accuracy = correct / total
    logger.info(f"📊 模型准确率: {correct}/{total} = {accuracy:.4f}")
    return accuracy

def setup_logging(log_file_path):
    """设置文件日志记录"""
    file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.info(f"日志文件已创建: {log_file_path}")
    return file_handler

def save_encoding_results(results, output_path):
    """保存编码结果到JSON文件"""
    # 转换numpy数组为列表以便JSON序列化
    def convert_for_json(obj):
        if isinstance(obj, dict):
            return {key: convert_for_json(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_for_json(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int32, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.float32, np.float64)):
            return float(obj)
        else:
            return obj
    
    results_json = convert_for_json(results)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results_json, f, indent=2, ensure_ascii=False)
    logger.info(f"编码结果已保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='BERT模型哈夫曼编码')
    parser.add_argument('--input_path', type=str, 
                       default='/download/model/quantized_model_8bit.pt',
                       help='8位量化模型路径')
    parser.add_argument('--output_dir', type=str, 
                       default='/download/',
                       help='输出目录')
    parser.add_argument('--no_cuda', action='store_true', default=False,
                       help='禁用CUDA')
    parser.add_argument('--evaluate', action='store_true', default=True,
                       help='评估编码前后的模型性能')
    parser.add_argument('--test_decode', action='store_true', default=True,
                       help='测试编码解码过程')
    
    args = parser.parse_args()
    
    # 创建保存目录
    setup_save_directories()
    
    # 设置日志文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file_path = f"/download/other/bert_huffman_encode_{timestamp}.log"
    file_handler = setup_logging(log_file_path)
    
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() and not args.no_cuda else 'cpu')
    logger.info(f"使用设备: {device}")
    logger.info(f"输入模型: {args.input_path}")
    
    # 检查输入文件
    if not os.path.exists(args.input_path):
        logger.error(f"❌ 输入文件不存在: {args.input_path}")
        return
    
    # 初始化结果字典
    results = {
        "timestamp": datetime.now().isoformat(),
        "input_path": args.input_path,
        "device": str(device),
        "parameters": vars(args)
    }
    
    try:
        # 计算原始文件大小
        original_file_size = os.path.getsize(args.input_path) / 1024 / 1024
        logger.info(f"📁 原始8位量化模型大小: {original_file_size:.2f} MB")
        results["original_file_size_mb"] = round(original_file_size, 2)
        
        # 哈夫曼编码
        logger.info("\n🚀 开始哈夫曼编码...")
        encoding_stats, encoding_dir = huffman_encode_quantized_model(args.input_path, args.output_dir)
        
        # 计算编码后的总文件大小
        encoded_file_size = 0
        for root, dirs, files in os.walk(encoding_dir):
            for file in files:
                encoded_file_size += os.path.getsize(os.path.join(root, file))
        encoded_file_size = encoded_file_size / 1024 / 1024
        
        # 计算压缩比
        file_compression_ratio = original_file_size / encoded_file_size if encoded_file_size > 0 else 0
        data_compression_ratio = encoding_stats['compression_ratio']
        
        logger.info(f"\n📊 压缩统计:")
        logger.info(f"   文件大小: {original_file_size:.2f} MB -> {encoded_file_size:.2f} MB")
        logger.info(f"   文件压缩比: {file_compression_ratio:.2f}x")
        logger.info(f"   数据压缩比: {data_compression_ratio:.2f}x")
        
        results["compression_stats"] = {
            "original_data_size_bytes": encoding_stats['total_original_size'],
            "encoded_data_size_bytes": encoding_stats['total_encoded_size'],
            "data_compression_ratio": round(data_compression_ratio, 2),
            "encoded_file_size_mb": round(encoded_file_size, 2),
            "file_compression_ratio": round(file_compression_ratio, 2)
        }
        results["encoding_stats"] = encoding_stats
        
        # 评估性能（如果需要）
        if args.evaluate:
            logger.info("\n🧪 性能评估...")

            # 加载分词器
            tokenizer = AutoTokenizer.from_pretrained("textattack/bert-base-uncased-ag-news")

            # 评估原始量化模型（如果测试解码）
            if args.test_decode:
                logger.info("📊 解码并评估模型...")
                decoded_model = decode_huffman_encoded_model(encoding_dir, device)
                decoded_accuracy = evaluate_model(decoded_model, tokenizer, device)

                results["performance"] = {
                    "decoded_accuracy": round(decoded_accuracy, 4)
                }

                logger.info(f"✅ 解码后模型准确率: {decoded_accuracy:.4f}")
        
        # 保存结果
        results_file_path = f"/download/other/bert_huffman_encode_results_{timestamp}.json"
        save_encoding_results(results, results_file_path)
        
        # 创建总结文件
        summary_file_path = f"/download/other/bert_huffman_encode_summary_{timestamp}.txt"
        with open(summary_file_path, 'w', encoding='utf-8') as f:
            f.write("BERT模型哈夫曼编码结果总结\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"编码时间: {results['timestamp']}\n")
            f.write(f"输入模型: {results['input_path']}\n")
            f.write(f"设备: {results['device']}\n\n")
            
            f.write("压缩统计:\n")
            f.write("-" * 20 + "\n")
            f.write(f"原始文件大小: {results['original_file_size_mb']} MB\n")
            f.write(f"编码后文件大小: {results['compression_stats']['encoded_file_size_mb']} MB\n")
            f.write(f"文件压缩比: {results['compression_stats']['file_compression_ratio']}x\n")
            f.write(f"数据压缩比: {results['compression_stats']['data_compression_ratio']}x\n\n")
            
            f.write(f"原始数据大小: {results['compression_stats']['original_data_size_bytes']:,} bytes\n")
            f.write(f"编码后数据大小: {results['compression_stats']['encoded_data_size_bytes']:,} bytes\n\n")
            
            if 'performance' in results:
                f.write("性能评估:\n")
                f.write("-" * 20 + "\n")
                f.write(f"解码后模型准确率: {results['performance']['decoded_accuracy']}\n\n")
            
            f.write("输出文件:\n")
            f.write("-" * 20 + "\n")
            f.write(f"编码文件目录: {encoding_dir}\n")
            f.write(f"详细日志: {log_file_path}\n")
            f.write(f"结果JSON: {results_file_path}\n")
            f.write(f"总结文件: {summary_file_path}\n")
        
        logger.info(f"📝 总结文件已保存到: {summary_file_path}")
        
        # 打包编码文件
        import tarfile
        archive_path = f"/download/model/bert_huffman_encoded_{timestamp}.tar.gz"
        with tarfile.open(archive_path, "w:gz") as tar:
            tar.add(encoding_dir, arcname=f"bert_huffman_encoded_{timestamp}")
        
        archive_size = os.path.getsize(archive_path) / 1024 / 1024
        total_compression_ratio = original_file_size / archive_size if archive_size > 0 else 0
        
        logger.info(f"📦 压缩包已创建: {archive_path}")
        logger.info(f"📊 压缩包大小: {archive_size:.2f} MB")
        logger.info(f"🎯 总压缩比: {total_compression_ratio:.2f}x")
        
        # 更新结果
        results["output_files"] = {
            "encoding_directory": encoding_dir,
            "compressed_archive": archive_path,
            "archive_size_mb": round(archive_size, 2),
            "total_compression_ratio": round(total_compression_ratio, 2),
            "log_file": log_file_path,
            "results_json": results_file_path,
            "summary_file": summary_file_path
        }
        
        # 重新保存更新后的结果
        save_encoding_results(results, results_file_path)
        
        logger.info("\n" + "="*50)
        logger.info("🎉 哈夫曼编码完成！")
        logger.info(f"📁 所有文件已保存到 /download/ 目录")
        logger.info(f"🗜️  最终压缩比: {total_compression_ratio:.2f}x")
        logger.info(f"📦 压缩包: {archive_path}")
        logger.info("="*50)
        
    except Exception as e:
        logger.error(f"❌ 编码过程出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭文件日志处理器
        logger.removeHandler(file_handler)
        file_handler.close()

if __name__ == '__main__':
    main()
